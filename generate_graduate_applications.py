#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
研究生申请记录数据生成器
根据现有的大学和专业数据，生成2000条中国学生申请研究生的记录
"""

import csv
import random
import pandas as pd
from datetime import datetime, timedelta

class GraduateApplicationGenerator:
    def __init__(self):
        """初始化数据生成器"""
        self.universities = []
        self.degree_programs = []
        self.chinese_universities = self.init_chinese_universities()
        self.chinese_names = self.init_chinese_names()
        self.majors_mapping = self.init_majors_mapping()
        
    def init_chinese_universities(self):
        """初始化中国大学数据"""
        return {
            '985': [
                ('清华大学', '985&211'),
                ('北京大学', '985&211'),
                ('复旦大学', '985&211'),
                ('上海交通大学', '985&211'),
                ('浙江大学', '985&211'),
                ('中国科学技术大学', '985&211'),
                ('南京大学', '985&211'),
                ('华中科技大学', '985&211'),
                ('中山大学', '985&211'),
                ('哈尔滨工业大学', '985&211'),
                ('西安交通大学', '985&211'),
                ('北京理工大学', '985&211'),
                ('同济大学', '985&211'),
                ('天津大学', '985&211'),
                ('华南理工大学', '985&211'),
                ('北京航空航天大学', '985&211'),
                ('东南大学', '985&211'),
                ('西北工业大学', '985&211'),
                ('大连理工大学', '985&211'),
                ('华东师范大学', '985&211'),
                ('北京师范大学', '985&211'),
                ('中南大学', '985&211'),
                ('四川大学', '985&211'),
                ('电子科技大学', '985&211'),
                ('厦门大学', '985&211'),
                ('山东大学', '985&211'),
                ('湖南大学', '985&211'),
                ('重庆大学', '985&211'),
                ('吉林大学', '985&211'),
                ('南开大学', '985&211')
            ],
            '211': [
                ('北京交通大学', '211'),
                ('北京工业大学', '211'),
                ('北京科技大学', '211'),
                ('北京化工大学', '211'),
                ('北京邮电大学', '211'),
                ('北京林业大学', '211'),
                ('北京中医药大学', '211'),
                ('首都师范大学', '211'),
                ('中央财经大学', '211'),
                ('对外经济贸易大学', '211'),
                ('华北电力大学', '211'),
                ('中国矿业大学', '211'),
                ('河海大学', '211'),
                ('江南大学', '211'),
                ('南京理工大学', '211'),
                ('南京航空航天大学', '211'),
                ('苏州大学', '211'),
                ('安徽大学', '211'),
                ('合肥工业大学', '211'),
                ('福州大学', '211'),
                ('南昌大学', '211'),
                ('郑州大学', '双一流'),
                ('华中师范大学', '211'),
                ('中南财经政法大学', '211'),
                ('湖南师范大学', '211'),
                ('暨南大学', '211'),
                ('华南师范大学', '211'),
                ('广西大学', '211'),
                ('海南大学', '211'),
                ('西南大学', '211')
            ],
            '双一流': [
                ('中国科学院大学', '双一流'),
                ('上海科技大学', '双一流'),
                ('南方科技大学', '双一流'),
                ('西湖大学', '双一流'),
                ('中国社会科学院大学', '双一流'),
                ('上海纽约大学', '双一流'),
                ('昆山杜克大学', '双一流'),
                ('宁波诺丁汉大学', '双一流'),
                ('西交利物浦大学', '双一流'),
                ('深圳北理莫斯科大学', '双一流')
            ],
            '普通本科': [
                ('北京工商大学', '普通本科'),
                ('北京信息科技大学', '普通本科'),
                ('首都经济贸易大学', '普通本科'),
                ('天津理工大学', '普通本科'),
                ('天津工业大学', '普通本科'),
                ('河北工业大学', '普通本科'),
                ('山西大学', '普通本科'),
                ('内蒙古大学', '普通本科'),
                ('沈阳工业大学', '普通本科'),
                ('大连交通大学', '普通本科'),
                ('长春理工大学', '普通本科'),
                ('哈尔滨理工大学', '普通本科'),
                ('上海理工大学', '普通本科'),
                ('上海海事大学', '普通本科'),
                ('江苏科技大学', '普通本科'),
                ('南京工业大学', '普通本科'),
                ('浙江理工大学', '普通本科'),
                ('杭州电子科技大学', '普通本科'),
                ('安徽理工大学', '普通本科'),
                ('福建理工大学', '普通本科'),
                ('江西理工大学', '普通本科'),
                ('山东理工大学', '普通本科'),
                ('河南理工大学', '普通本科'),
                ('武汉理工大学', '普通本科'),
                ('湖南理工学院', '普通本科'),
                ('广东工业大学', '普通本科'),
                ('桂林理工大学', '普通本科'),
                ('重庆理工大学', '普通本科'),
                ('西南石油大学', '普通本科'),
                ('西安理工大学', '普通本科')
            ],
            '专科': [
                ('北京电子科技职业学院', '专科'),
                ('天津职业大学', '专科'),
                ('河北工业职业技术学院', '专科'),
                ('山西职业技术学院', '专科'),
                ('辽宁省交通高等专科学校', '专科'),
                ('长春职业技术学院', '专科'),
                ('黑龙江建筑职业技术学院', '专科'),
                ('上海工艺美术职业学院', '专科'),
                ('江苏建筑职业技术学院', '专科'),
                ('浙江机电职业技术学院', '专科'),
                ('安徽职业技术学院', '专科'),
                ('福建船政交通职业学院', '专科'),
                ('江西现代职业技术学院', '专科'),
                ('山东商业职业技术学院', '专科'),
                ('河南工业职业技术学院', '专科'),
                ('武汉职业技术学院', '专科'),
                ('湖南铁道职业技术学院', '专科'),
                ('广东轻工职业技术学院', '专科'),
                ('广西机电职业技术学院', '专科'),
                ('重庆工业职业技术学院', '专科')
            ]
        }
    
    def init_chinese_names(self):
        """初始化中文姓名数据"""
        surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕', '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎']
        
        male_names = ['伟', '强', '磊', '军', '勇', '涛', '明', '超', '亮', '华', '建', '国', '峰', '辉', '鹏', '志', '杰', '宇', '斌', '龙', '飞', '凯', '浩', '博', '文', '武', '康', '健', '俊', '帅', '阳', '刚', '毅', '豪', '雄', '威', '力', '成', '达', '胜']
        
        female_names = ['丽', '娜', '敏', '静', '秀', '美', '艳', '芳', '燕', '红', '霞', '玲', '梅', '莉', '兰', '英', '萍', '雪', '月', '晶', '欣', '婷', '雅', '琳', '洁', '慧', '颖', '蕾', '薇', '倩', '妍', '瑶', '琪', '珊', '宁', '佳', '怡', '悦', '嘉', '思']
        
        return {
            'surnames': surnames,
            'male_names': male_names,
            'female_names': female_names
        }
    
    def init_majors_mapping(self):
        """初始化专业映射关系"""
        return {
            '计算机类': ['计算机科学与技术', '软件工程', '网络工程', '信息安全', '数据科学与大数据技术', '人工智能', '物联网工程', '数字媒体技术'],
            '电子信息类': ['电子信息工程', '通信工程', '电子科学与技术', '微电子科学与工程', '光电信息科学与工程', '信息工程'],
            '机械类': ['机械工程', '机械设计制造及其自动化', '材料成型及控制工程', '机械电子工程', '工业设计', '过程装备与控制工程'],
            '土木类': ['土木工程', '建筑环境与能源应用工程', '给排水科学与工程', '建筑电气与智能化', '城市地下空间工程'],
            '化工与制药类': ['化学工程与工艺', '制药工程', '轻化工程', '能源化学工程'],
            '数学类': ['数学与应用数学', '信息与计算科学', '统计学', '应用统计学'],
            '物理学类': ['物理学', '应用物理学', '核物理', '声学'],
            '化学类': ['化学', '应用化学', '化学生物学', '分子科学与工程'],
            '生物科学类': ['生物科学', '生物技术', '生物信息学', '生态学'],
            '经济学类': ['经济学', '经济统计学', '财政学', '金融学', '金融工程', '保险学', '投资学', '国际经济与贸易'],
            '管理学类': ['工商管理', '市场营销', '会计学', '财务管理', '人力资源管理', '审计学', '资产评估', '物业管理', '文化产业管理'],
            '医学类': ['临床医学', '口腔医学', '预防医学', '中医学', '针灸推拿学', '中西医临床医学', '药学', '中药学', '医学检验技术', '医学影像技术'],
            '文学类': ['汉语言文学', '汉语国际教育', '英语', '俄语', '德语', '法语', '西班牙语', '阿拉伯语', '日语', '朝鲜语'],
            '法学类': ['法学', '知识产权', '监狱学'],
            '教育学类': ['教育学', '科学教育', '人文教育', '教育技术学', '艺术教育', '学前教育', '小学教育'],
            '艺术学类': ['音乐表演', '音乐学', '作曲与作曲技术理论', '舞蹈表演', '舞蹈学', '舞蹈编导', '表演', '戏剧学', '电影学', '戏剧影视文学', '广播电视编导', '戏剧影视导演', '动画', '美术学', '绘画', '雕塑', '摄影', '书法学', '中国画', '实验艺术', '跨媒体艺术', '文物保护与修复', '漫画', '艺术设计学', '视觉传达设计', '环境设计', '产品设计', '服装与服饰设计', '公共艺术', '工艺美术', '数字媒体艺术', '艺术与科技', '陶瓷艺术设计', '新媒体艺术', '包装设计']
        }
    
    def load_data(self):
        """加载现有的大学和专业数据"""
        try:
            # 加载大学数据
            with open('universities.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                self.universities = list(reader)
            
            # 加载专业数据
            with open('degree_programs.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                self.degree_programs = list(reader)
                
            print(f"已加载 {len(self.universities)} 所大学和 {len(self.degree_programs)} 个专业")
            
        except FileNotFoundError as e:
            print(f"文件未找到: {e}")
            return False
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return False
        
        return True
    
    def generate_chinese_name(self, gender):
        """生成中文姓名"""
        surname = random.choice(self.chinese_names['surnames'])
        if gender == '男':
            given_name = random.choice(self.chinese_names['male_names'])
            if random.random() < 0.3:  # 30%概率生成双字名
                given_name += random.choice(self.chinese_names['male_names'])
        else:
            given_name = random.choice(self.chinese_names['female_names'])
            if random.random() < 0.3:  # 30%概率生成双字名
                given_name += random.choice(self.chinese_names['female_names'])
        
        return surname + given_name
    
    def generate_applicant_id(self):
        """生成申请者ID"""
        return str(random.randint(1000000000, 9999999999))
    
    def select_chinese_university(self):
        """选择中国大学"""
        # 根据分布比例选择大学类型
        rand = random.random()
        if rand < 0.30:  # 30% 985
            university_type = '985'
        elif rand < 0.70:  # 40% 211
            university_type = '211'
        elif rand < 0.90:  # 20% 双一流
            university_type = '双一流'
        elif rand < 0.98:  # 8% 普通本科
            university_type = '普通本科'
        else:  # 2% 专科
            university_type = '专科'
        
        university_name, institution_type = random.choice(self.chinese_universities[university_type])
        
        # 根据大学类型确定学位层次
        if university_type == '专科':
            degree_level = '专科'
        else:
            degree_level = '本科'
            
        return university_name, institution_type, degree_level
    
    def generate_major(self, university_type):
        """根据大学类型生成专业"""
        # 根据大学类型调整专业分布
        if university_type in ['985&211', '211']:
            # 985/211更倾向于理工科
            major_categories = ['计算机类', '电子信息类', '机械类', '数学类', '物理学类', '化学类', '经济学类', '管理学类']
            weights = [0.25, 0.15, 0.15, 0.10, 0.10, 0.05, 0.10, 0.10]
        elif university_type == '双一流':
            major_categories = ['计算机类', '电子信息类', '生物科学类', '数学类', '经济学类', '管理学类']
            weights = [0.20, 0.15, 0.15, 0.15, 0.15, 0.20]
        elif university_type == '普通本科':
            major_categories = ['计算机类', '机械类', '土木类', '经济学类', '管理学类', '文学类']
            weights = [0.20, 0.15, 0.15, 0.20, 0.20, 0.10]
        else:  # 专科
            major_categories = ['计算机类', '机械类', '土木类', '管理学类']
            weights = [0.30, 0.25, 0.25, 0.20]
        
        category = random.choices(major_categories, weights=weights)[0]
        return random.choice(self.majors_mapping[category])

    def generate_gpa(self, university_type):
        """根据大学类型生成GPA"""
        if university_type == '985&211':
            return round(random.uniform(3.4, 3.9), 1)
        elif university_type == '211':
            return round(random.uniform(3.2, 3.8), 1)
        elif university_type == '双一流':
            return round(random.uniform(3.1, 3.7), 1)
        elif university_type == '普通本科':
            return round(random.uniform(2.8, 3.5), 1)
        else:  # 专科
            return round(random.uniform(2.5, 3.2), 1)

    def generate_language_scores(self, university_type, target_country):
        """根据大学类型和目标国家生成语言成绩"""
        toefl_score = None
        ielts_score = None

        # 根据大学类型确定分数范围
        if university_type == '985&211':
            toefl_range = (95, 115)
            ielts_range = (6.5, 8.0)
        elif university_type == '211':
            toefl_range = (85, 105)
            ielts_range = (6.0, 7.5)
        elif university_type == '双一流':
            toefl_range = (80, 100)
            ielts_range = (6.0, 7.0)
        elif university_type == '普通本科':
            toefl_range = (70, 95)
            ielts_range = (5.5, 6.5)
        else:  # 专科
            toefl_range = (60, 85)
            ielts_range = (5.0, 6.0)

        # 根据目标国家决定考哪种语言考试
        if target_country in ['美国', '加拿大']:
            if random.random() < 0.7:  # 70%考托福
                toefl_score = random.randint(toefl_range[0], toefl_range[1])
            else:
                ielts_score = round(random.uniform(ielts_range[0], ielts_range[1]), 1)
        elif target_country in ['英国', '澳大利亚', '新西兰']:
            if random.random() < 0.8:  # 80%考雅思
                ielts_score = round(random.uniform(ielts_range[0], ielts_range[1]), 1)
            else:
                toefl_score = random.randint(toefl_range[0], toefl_range[1])
        else:
            # 其他国家随机选择
            if random.random() < 0.5:
                toefl_score = random.randint(toefl_range[0], toefl_range[1])
            else:
                ielts_score = round(random.uniform(ielts_range[0], ielts_range[1]), 1)

        return toefl_score, ielts_score

    def generate_gre_scores(self, university_type, target_major):
        """根据大学类型和目标专业生成GRE成绩"""
        # 某些专业不需要GRE
        if any(keyword in target_major for keyword in ['工商管理', 'MBA', '法学', '医学', '艺术']):
            return None, None, None

        # 根据大学类型确定GRE分数范围
        if university_type == '985&211':
            verbal_range = (155, 170)
            quant_range = (160, 170)
            writing_range = (3.5, 5.0)
        elif university_type == '211':
            verbal_range = (150, 165)
            quant_range = (155, 168)
            writing_range = (3.0, 4.5)
        elif university_type == '双一流':
            verbal_range = (145, 160)
            quant_range = (150, 165)
            writing_range = (3.0, 4.0)
        elif university_type == '普通本科':
            verbal_range = (140, 155)
            quant_range = (145, 160)
            writing_range = (2.5, 3.5)
        else:  # 专科
            verbal_range = (135, 150)
            quant_range = (140, 155)
            writing_range = (2.0, 3.0)

        # 理工科学生数学分数通常更高
        if any(keyword in target_major for keyword in ['计算机', '数学', '物理', '工程', '统计']):
            quant_range = (quant_range[0] + 5, min(170, quant_range[1] + 5))

        gre_verbal = random.randint(verbal_range[0], verbal_range[1])
        gre_quant = random.randint(quant_range[0], quant_range[1])
        gre_writing = round(random.uniform(writing_range[0], writing_range[1]), 1)

        return gre_verbal, gre_quant, gre_writing

    def select_target_country(self):
        """选择目标国家"""
        countries = ['美国', '英国', '加拿大', '澳大利亚', '德国', '法国', '新加坡', '荷兰', '瑞士', '日本']
        weights = [0.40, 0.25, 0.15, 0.10, 0.03, 0.02, 0.02, 0.01, 0.01, 0.01]
        return random.choices(countries, weights=weights)[0]

    def select_target_university_and_major(self, target_country, applicant_major):
        """根据目标国家和申请者专业选择目标大学和专业"""
        # 过滤目标国家的大学
        target_universities = []
        country_mapping = {
            '美国': 'United States',
            '英国': 'United Kingdom',
            '加拿大': 'Canada',
            '澳大利亚': 'Australia',
            '德国': 'Germany',
            '法国': 'France',
            '新加坡': 'Singapore',
            '荷兰': 'Netherlands',
            '瑞士': 'Switzerland',
            '日本': 'Japan'
        }

        target_country_en = country_mapping.get(target_country, target_country)

        for uni in self.universities:
            if uni['country'] == target_country_en:
                target_universities.append(uni)

        if not target_universities:
            # 如果没有找到对应国家的大学，随机选择一个
            target_universities = self.universities[:10]  # 选择前10所大学

        # 选择目标大学
        target_university = random.choice(target_universities)

        # 根据申请者专业匹配目标专业
        target_programs = []
        for program in self.degree_programs:
            if program['university_id'] == target_university['university_id']:
                target_programs.append(program)

        if target_programs:
            # 尝试匹配相关专业
            related_programs = []
            for program in target_programs:
                if self.is_related_major(applicant_major, program['program_name']):
                    related_programs.append(program)

            if related_programs:
                target_program = random.choice(related_programs)
            else:
                target_program = random.choice(target_programs)
        else:
            # 如果没有找到专业，创建一个默认的
            target_program = {
                'program_name': '计算机科学',
                'university_id': target_university['university_id']
            }

        return target_university['university_name_cn'], target_program['program_name']

    def is_related_major(self, applicant_major, target_major):
        """判断专业是否相关"""
        # 简单的专业匹配逻辑
        keywords_mapping = {
            '计算机': ['计算机', '人工智能', '软件', '数据'],
            '电子': ['电子', '电气', '通信'],
            '机械': ['机械', '制造', '工程'],
            '数学': ['数学', '统计', '数据'],
            '物理': ['物理', '材料'],
            '化学': ['化学', '材料'],
            '生物': ['生物', '医学'],
            '经济': ['经济', '金融', '商业', '管理'],
            '管理': ['管理', '商业', '金融'],
            '医学': ['医学', '临床', '生物']
        }

        for keyword, related_keywords in keywords_mapping.items():
            if keyword in applicant_major:
                for related_keyword in related_keywords:
                    if related_keyword in target_major:
                        return True

        return False

    def generate_research_experience(self, university_type, major):
        """生成科研经历"""
        # 根据大学类型确定有科研经历的概率
        if university_type == '985&211':
            has_research = random.random() < 0.7  # 70%概率
        elif university_type == '211':
            has_research = random.random() < 0.5  # 50%概率
        elif university_type == '双一流':
            has_research = random.random() < 0.4  # 40%概率
        elif university_type == '普通本科':
            has_research = random.random() < 0.2  # 20%概率
        else:  # 专科
            has_research = random.random() < 0.05  # 5%概率

        return '是' if has_research else '否'

    def generate_internships(self, university_type, major):
        """生成实习经历"""
        companies = {
            '计算机': ['腾讯', '阿里巴巴', '百度', '字节跳动', '华为', '小米', '美团', '滴滴', '京东', '网易'],
            '电子': ['华为', '中兴', '小米', 'OPPO', 'vivo', '联想', '海康威视', '大疆', '紫光集团', '中芯国际'],
            '机械': ['中国中车', '三一重工', '中联重科', '徐工集团', '潍柴动力', '上汽集团', '比亚迪', '吉利汽车', '长城汽车', '奇瑞汽车'],
            '金融': ['中国银行', '工商银行', '建设银行', '农业银行', '招商银行', '平安银行', '中信银行', '光大银行', '民生银行', '兴业银行'],
            '咨询': ['麦肯锡', '波士顿咨询', '贝恩咨询', '德勤', '普华永道', '毕马威', '安永', '埃森哲', '罗兰贝格', 'IBM'],
            '互联网': ['腾讯', '阿里巴巴', '百度', '字节跳动', '美团', '滴滴', '京东', '网易', '新浪', '搜狐'],
            '制造': ['富士康', '比亚迪', '格力电器', '美的集团', '海尔集团', '联想集团', '华为', '小米', 'TCL', '海信']
        }

        # 根据专业选择公司类型
        if '计算机' in major or '软件' in major or '人工智能' in major:
            company_list = companies['计算机'] + companies['互联网']
        elif '电子' in major or '通信' in major:
            company_list = companies['电子']
        elif '机械' in major or '工程' in major:
            company_list = companies['机械'] + companies['制造']
        elif '金融' in major or '经济' in major:
            company_list = companies['金融'] + companies['咨询']
        else:
            company_list = companies['互联网'] + companies['制造']

        # 根据大学类型确定有实习经历的概率
        if university_type == '985&211':
            has_internship = random.random() < 0.8  # 80%概率
        elif university_type == '211':
            has_internship = random.random() < 0.6  # 60%概率
        elif university_type == '双一流':
            has_internship = random.random() < 0.5  # 50%概率
        elif university_type == '普通本科':
            has_internship = random.random() < 0.3  # 30%概率
        else:  # 专科
            has_internship = random.random() < 0.2  # 20%概率

        if has_internship:
            return random.choice(company_list)
        else:
            return '暂无'

    def generate_publications(self, university_type, research_experience):
        """生成发表论文情况"""
        if research_experience == '否':
            return '暂无'

        # 根据大学类型确定有发表论文的概率
        if university_type == '985&211':
            has_publication = random.random() < 0.3  # 30%概率
        elif university_type == '211':
            has_publication = random.random() < 0.15  # 15%概率
        elif university_type == '双一流':
            has_publication = random.random() < 0.1  # 10%概率
        else:
            has_publication = random.random() < 0.05  # 5%概率

        if has_publication:
            publication_types = ['会议论文', '期刊论文', '专利', '软件著作权']
            return random.choice(publication_types)
        else:
            return '暂无'

    def generate_admission_result(self, university_type, gpa, toefl_score, ielts_score, gre_verbal, target_university):
        """根据申请者背景生成录取结果"""
        # 计算申请者综合实力分数
        score = 0

        # 大学背景分数
        if university_type == '985&211':
            score += 40
        elif university_type == '211':
            score += 30
        elif university_type == '双一流':
            score += 25
        elif university_type == '普通本科':
            score += 15
        else:  # 专科
            score += 5

        # GPA分数
        score += (gpa - 2.0) * 15  # GPA每0.1分值1.5分

        # 语言成绩分数
        if toefl_score:
            score += (toefl_score - 60) / 2  # TOEFL每2分值1分
        elif ielts_score:
            score += (ielts_score - 5.0) * 10  # IELTS每0.1分值1分

        # GRE分数
        if gre_verbal:
            score += (gre_verbal - 130) / 2  # GRE Verbal每2分值1分

        # 目标学校难度调整
        target_qs_ranking = None
        for uni in self.universities:
            if uni['university_name_cn'] == target_university:
                target_qs_ranking = int(uni['qs_ranking'])
                break

        if target_qs_ranking:
            if target_qs_ranking <= 10:
                score -= 20  # 顶级学校难度很高
            elif target_qs_ranking <= 30:
                score -= 10  # 顶级学校难度高
            elif target_qs_ranking <= 50:
                score -= 5   # 优秀学校难度中等

        # 根据分数确定录取结果
        if score >= 80:
            results = ['Admitted', 'Admitted', 'Admitted', 'Waitlisted']
            weights = [0.7, 0.2, 0.05, 0.05]
        elif score >= 60:
            results = ['Admitted', 'Waitlisted', 'Rejected']
            weights = [0.4, 0.3, 0.3]
        elif score >= 40:
            results = ['Admitted', 'Waitlisted', 'Rejected']
            weights = [0.2, 0.3, 0.5]
        else:
            results = ['Waitlisted', 'Rejected', 'Rejected']
            weights = [0.1, 0.45, 0.45]

        admission_result = random.choices(results, weights=weights)[0]

        # 确定录取学校和专业
        if admission_result == 'Admitted':
            admission_school = target_university
            # 这里简化处理，假设录取的就是申请的专业
            admission_major = None  # 将在主函数中设置
        else:
            admission_school = '暂无'
            admission_major = '暂无'

        return admission_result, admission_school, admission_major

    def generate_scholarship(self, admission_result, university_type, gpa):
        """生成奖学金情况"""
        if admission_result != 'Admitted':
            return '否'

        # 根据背景确定获得奖学金的概率
        scholarship_prob = 0
        if university_type == '985&211' and gpa >= 3.6:
            scholarship_prob = 0.4
        elif university_type == '211' and gpa >= 3.5:
            scholarship_prob = 0.25
        elif university_type == '双一流' and gpa >= 3.4:
            scholarship_prob = 0.15
        elif gpa >= 3.3:
            scholarship_prob = 0.1

        return '是' if random.random() < scholarship_prob else '否'

    def generate_single_application(self):
        """生成单个申请记录"""
        # 生成申请者ID
        applicant_id = self.generate_applicant_id()

        # 生成性别和姓名
        gender = random.choice(['男', '女'])
        applicant_name = self.generate_chinese_name(gender)

        # 生成年龄（本科毕业生通常22-24岁，专科毕业生20-22岁）
        age = random.randint(20, 26)

        # 选择中国大学背景
        institution_name, institution_type, degree_level = self.select_chinese_university()

        # 生成GPA
        gpa = self.generate_gpa(institution_type)

        # 生成专业
        major = self.generate_major(institution_type)

        # 生成毕业年份
        degree_years = ['2022-06', '2022-07', '2023-06', '2023-07', '2024-06', '2024-07']
        degree_year = random.choice(degree_years)

        # 选择目标国家
        target_country = self.select_target_country()

        # 生成语言成绩
        toefl_score, ielts_score = self.generate_language_scores(institution_type, target_country)

        # 生成GRE成绩
        gre_verbal, gre_quant, gre_writing = self.generate_gre_scores(institution_type, major)

        # 选择目标大学和专业
        target_university, target_major = self.select_target_university_and_major(target_country, major)

        # 生成申请学期
        preferred_terms = ['2025 Fall', '2025 Spring', '2026 Fall']
        preferred_term = random.choice(preferred_terms)

        # 生成科研经历
        research_experience = self.generate_research_experience(institution_type, major)

        # 生成实习经历
        internships = self.generate_internships(institution_type, major)

        # 生成发表论文
        publications = self.generate_publications(institution_type, research_experience)

        # 生成面试要求和结果
        interview_required = random.choice(['是', '否'])
        if interview_required == '是':
            interview_results = ['优秀', '良好', '一般', '较差']
            interview_result = random.choice(interview_results)
        else:
            interview_result = '无'

        # 生成录取结果
        admission_result, admission_school, admission_major = self.generate_admission_result(
            institution_type, gpa, toefl_score, ielts_score, gre_verbal, target_university
        )

        # 如果被录取，设置录取专业
        if admission_result == 'Admitted':
            admission_major = target_major

        # 生成奖学金情况
        scholarship_awarded = self.generate_scholarship(admission_result, institution_type, gpa)

        # 确定是否被录取
        is_admitted = '是' if admission_result == 'Admitted' else '否'

        return {
            'applicant_id': applicant_id,
            'applicant_name': applicant_name,
            'gender': gender,
            'age': age,
            'degree_level': degree_level,
            'institution_name': institution_name,
            'institution_type': institution_type,
            'gpa': gpa,
            'major': major,
            'degree_year': degree_year,
            'toefl_score': toefl_score if toefl_score else '',
            'ielts_score': ielts_score if ielts_score else '',
            'gre_verbal': gre_verbal if gre_verbal else '',
            'gre_quant': gre_quant if gre_quant else '',
            'gre_writing': gre_writing if gre_writing else '',
            'target_country': target_country,
            'target_universities': target_university,
            'target_majors': target_major,
            'preferred_term': preferred_term,
            'research_experience': research_experience,
            'internships': internships,
            'publications': publications,
            'interview_required': interview_required,
            'interview_result': interview_result,
            'admission_result': admission_result,
            'admission_school': admission_school,
            'admission_major': admission_major,
            'scholarship_awarded': scholarship_awarded,
            'is_admitted': is_admitted
        }

    def generate_applications(self, num_records=2000):
        """生成指定数量的申请记录"""
        if not self.load_data():
            print("数据加载失败，无法生成申请记录")
            return

        print(f"开始生成 {num_records} 条申请记录...")

        applications = []
        for i in range(num_records):
            if (i + 1) % 100 == 0:
                print(f"已生成 {i + 1} 条记录...")

            application = self.generate_single_application()
            applications.append(application)

        # 保存到CSV文件
        self.save_to_csv(applications)
        print(f"成功生成 {num_records} 条申请记录并保存到 graduate_application.csv")

    def save_to_csv(self, applications):
        """保存申请记录到CSV文件"""
        fieldnames = [
            'applicant_id', 'applicant_name', 'gender', 'age', 'degree_level',
            'institution_name', 'institution_type', 'gpa', 'major', 'degree_year',
            'toefl_score', 'ielts_score', 'gre_verbal', 'gre_quant', 'gre_writing',
            'target_country', 'target_universities', 'target_majors', 'preferred_term',
            'research_experience', 'internships', 'publications', 'interview_required',
            'interview_result', 'admission_result', 'admission_school', 'admission_major',
            'scholarship_awarded', 'is_admitted'
        ]

        with open('graduate_application.csv', 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(applications)


def main():
    """主函数"""
    generator = GraduateApplicationGenerator()
    generator.generate_applications(2000)


if __name__ == "__main__":
    main()
